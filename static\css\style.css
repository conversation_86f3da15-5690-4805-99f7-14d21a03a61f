/* Custom CSS for Real Home - Zillow Clone */

:root {
    --primary-color: #006ba6;
    --secondary-color: #0496c7;
    --accent-color: #9bb1ff;
    --text-dark: #2c3e50;
    --text-light: #6c757d;
    --border-color: #e9ecef;
    --hover-color: #f8f9fa;
}

html, body {
    width: 100%;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
}

/* Navigation - Professional Layout */
.navbar {
    font-family: Inter, "Adjusted Arial", Tahoma, Geneva, sans-serif;
    font-size: 93.75%;
    position: relative;
    height: 80px;
    z-index: 1000;
    background-color: white;
    width: 100%;
    border-bottom: 1px solid #e6e6e6;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    height: 100%;
    position: static;
    width: 100%;
    max-width: 1280px;
    margin: auto;
    border: none;
    padding-right: 24px;
    padding-left: 24px;
    z-index: 1;
    overflow: visible;
    align-items: center;
}

.navbar-brand {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color) !important;
    text-decoration: none;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    height: 100%;
}

.navbar-brand:hover {
    color: var(--secondary-color) !important;
}

.navbar-brand i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.brand-text {
    font-weight: 700;
}

.navbar-nav {
    align-items: center;
    height: 100%;
    display: flex;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: #333 !important;
    transition: all 0.3s ease;
    padding: 0.75rem 1.25rem !important;
    margin: 0 0.25rem;
    border-radius: 6px;
    font-size: 0.95rem;
    text-decoration: none;
    position: relative;
    height: auto;
    display: flex;
    align-items: center;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background-color: rgba(0, 107, 166, 0.1);
}

.navbar-nav .nav-link.btn-signin {
    background-color: var(--primary-color);
    color: white !important;
    font-weight: 600;
    border-radius: 25px;
    padding: 0.6rem 1.5rem !important;
    margin-left: 0.5rem;
}

.navbar-nav .nav-link.btn-signin:hover {
    background-color: var(--secondary-color);
    color: white !important;
    transform: translateY(-1px);
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 107, 166, 0.25);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Main content padding for fixed navbar */
.main-content {
    padding-top: 80px;
}

/* Navbar scroll behavior */
.navbar.scrolled {
    height: 60px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Professional navbar layout for larger screens */
@media (min-width: 890px) {
    .navbar .container {
        display: flex;
        justify-content: space-between;
        flex-direction: row;
        height: 100%;
        position: static;
        width: 100%;
        max-width: 1280px;
        margin: auto;
        border: none;
        padding-right: 24px;
        padding-left: 24px;
        z-index: 1;
        overflow: visible;
    }

    .navbar-nav.me-auto {
        margin-left: 2rem !important;
    }

    .navbar-nav.ms-auto {
        margin-left: auto !important;
    }

    .navbar-collapse {
        display: flex !important;
        flex-basis: auto;
    }
}

/* Standard desktop breakpoint */
@media (min-width: 992px) {
    .navbar-nav .nav-link {
        padding: 0.75rem 1.5rem !important;
    }
}

/* Active nav link styling */
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    font-weight: 600;
}

.navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background-color: var(--primary-color);
    border-radius: 1px;
}

/* Professional box-sizing for all elements */
*, ::after, ::before {
    box-sizing: border-box;
}

/* Mobile menu overlay styling */
@media (max-width: 889px) {
    .navbar-collapse.show {
        display: flex !important;
        border-right: 1px solid rgb(173, 173, 182);
        max-width: 375px;
    }

    .navbar-collapse.collapsing {
        display: flex !important;
    }
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    /* Use online image (current) */
    background-image: url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
    /* To use local image, uncomment the line below and comment the line above */
    /* background-image: url('/static/images/hero-bg.jpg'); */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 800px;
    width: 100%;
    padding: 0 20px;
}

.hero-text {
    margin-bottom: 3rem;
}

.hero-main-title {
    font-size: 4.5rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-search {
    display: flex;
    justify-content: center;
}

.search-form {
    width: 100%;
    max-width: 600px;
}

.search-input-container {
    position: relative;
    display: flex;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.search-input {
    flex: 1;
    border: none;
    padding: 18px 20px;
    font-size: 16px;
    outline: none;
    background: transparent;
    color: #333;
}

.search-input::placeholder {
    color: #666;
    font-size: 16px;
}

.search-button {
    background: var(--primary-color);
    border: none;
    padding: 18px 24px;
    color: white;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-button:hover {
    background: var(--secondary-color);
}

.search-button i {
    font-size: 18px;
}

/* Property Cards */
.property-card {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: white;
    height: 100%;
}

.property-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    border-color: var(--primary-color);
}

.property-image {
    height: 220px;
    object-fit: cover;
    width: 100%;
    background-color: var(--hover-color);
}

.property-details {
    font-size: 0.9rem;
    color: var(--text-light);
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
}

.property-details span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.property-details i {
    color: var(--primary-color);
    font-size: 0.8rem;
}

.card-body {
    padding: 1.25rem;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    line-height: 1.3;
    color: var(--text-dark);
}

/* Search Page */
.search-filters {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-section {
    margin-bottom: 1rem;
}

.filter-section:last-child {
    margin-bottom: 0;
}

/* Property Detail Page */
.property-gallery {
    border-radius: 12px;
    overflow: hidden;
}

.property-main-image {
    height: 400px;
    object-fit: cover;
    width: 100%;
}

.property-thumbnail {
    height: 80px;
    object-fit: cover;
    width: 100%;
    cursor: pointer;
    border-radius: 8px;
    transition: opacity 0.3s ease;
}

.property-thumbnail:hover {
    opacity: 0.8;
}

.property-info-card {
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.property-price {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.property-features {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--hover-color);
    border-radius: 20px;
    font-size: 0.9rem;
}

.feature-item i {
    color: var(--primary-color);
}

/* Map */
.map-container {
    height: 400px;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

/* Forms */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 107, 166, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border-color: var(--border-color);
    padding: 0.75rem 1rem;
}

.pagination .page-link:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Layout Improvements */
.container {
    max-width: 1200px;
    padding-left: 15px;
    padding-right: 15px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}

/* Main content wrapper */
main {
    width: 100%;
    overflow-x: hidden;
}

/* Ensure sections don't overflow */
section {
    width: 100%;
    overflow-x: hidden;
}

/* Featured Properties Section */
.featured-properties {
    padding: 4rem 0;
}

.featured-properties h2 {
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 3rem;
}

/* Price Badge */
.badge {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
}

/* Address styling */
.card-text {
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* Responsive Design */
/* Large Desktop */
@media (min-width: 1200px) {
    .hero-main-title {
        font-size: 5.5rem;
    }

    .search-input-container {
        max-width: 700px;
    }
}

/* Desktop */
@media (min-width: 992px) {
    .hero-main-title {
        font-size: 5rem;
    }
}

/* Tablet */
@media (max-width: 991px) and (min-width: 768px) {
    .hero-section {
        min-height: 80vh;
    }

    .hero-main-title {
        font-size: 3.5rem;
    }

    .hero-content {
        padding: 0 15px;
    }
}

/* Mobile */
@media (max-width: 767px) {
    .hero-section {
        min-height: 70vh;
    }

    .hero-main-title {
        font-size: 2.5rem;
    }

    .hero-text {
        margin-bottom: 2rem;
    }

    .search-input {
        padding: 15px 18px;
        font-size: 14px;
    }

    .search-input::placeholder {
        font-size: 14px;
    }

    .search-button {
        padding: 15px 20px;
    }

    .search-button i {
        font-size: 16px;
    }

    /* Mobile navbar styles */
    .navbar {
        height: 70px;
    }

    .navbar .container {
        padding-right: 15px;
        padding-left: 15px;
    }

    .navbar-brand {
        font-size: 1.5rem;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem !important;
        margin: 0.25rem 0;
        text-align: center;
    }

    .navbar-nav .nav-link.btn-signin {
        margin: 0.5rem 0;
        display: inline-block;
        width: auto;
    }

    .navbar-collapse {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e6e6e6;
        position: fixed;
        height: calc(100% - 70px);
        z-index: 2;
        overflow: scroll;
        flex-direction: column;
        width: 100%;
        background: white;
        left: 0;
        top: 70px;
    }

    .main-content {
        padding-top: 70px;
    }

    .property-features {
        justify-content: center;
        gap: 0.5rem;
    }

    .search-filters {
        padding: 1rem;
    }

    .property-image {
        height: 200px;
    }

    .card-body {
        padding: 1rem;
    }

    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
}

/* Tablet navbar adjustments */
@media (max-width: 889px) and (min-width: 768px) {
    .navbar {
        height: 75px;
    }

    .navbar .container {
        padding-right: 20px;
        padding-left: 20px;
    }

    .navbar-nav .nav-link {
        padding: 0.6rem 0.8rem !important;
        font-size: 0.9rem;
    }

    .navbar-nav .nav-link.btn-signin {
        padding: 0.5rem 1.2rem !important;
    }

    .main-content {
        padding-top: 75px;
    }
}

/* Very small mobile devices */
@media (max-width: 576px) {
    .hero-section {
        min-height: 60vh;
    }

    .hero-main-title {
        font-size: 2rem;
    }

    .hero-content {
        padding: 0 10px;
    }

    .search-input {
        padding: 12px 15px;
        font-size: 14px;
    }

    .search-button {
        padding: 12px 18px;
    }

    /* Extra small mobile navbar */
    .navbar {
        height: 65px;
    }

    .navbar .container {
        padding-right: 10px;
        padding-left: 10px;
    }

    .navbar-brand {
        font-size: 1.3rem;
    }

    .navbar-brand i {
        font-size: 1.2rem;
    }

    .navbar-nav .nav-link {
        padding: 0.6rem 0.8rem !important;
        font-size: 0.9rem;
    }

    .navbar-collapse {
        top: 65px;
        height: calc(100% - 65px);
    }

    .main-content {
        padding-top: 65px;
    }

    .property-details {
        font-size: 0.8rem;
        gap: 0.5rem;
    }

    .container {
        padding-left: 5px;
        padding-right: 5px;
    }

    .col-lg-4, .col-md-6 {
        padding-left: 5px;
        padding-right: 5px;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}
